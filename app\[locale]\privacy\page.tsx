import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Navigation } from "@/components/navigation";
import { PageHeader } from "@/components/page-header";
import { locales } from '@/i18n';

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Navigation />
      <PageHeader
        title="隐私政策"
        description="了解我们如何保护您的隐私和数据安全"
      />

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="border-border">
          <CardHeader>
            <CardTitle className="text-3xl text-foreground">RayBoxUI 隐私政策</CardTitle>
            <p className="text-muted-foreground">最后更新：2024年8月22日</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">1. 信息收集</h2>
              <p className="text-muted-foreground leading-relaxed">
                RayBoxUI 是一个本地运行的节点管理工具。我们不会收集、存储或传输您的个人信息到我们的服务器。
                所有配置数据都存储在您的本地设备上。
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">2. 数据使用</h2>
              <p className="text-muted-foreground leading-relaxed">
                您在应用中输入的所有配置信息（包括服务器地址、用户名、密码等）仅用于：
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>连接到您指定的节点管理面板</li>
                <li>生成配置文件和二维码</li>
                <li>在您的设备上本地存储配置</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">3. 数据安全</h2>
              <p className="text-muted-foreground leading-relaxed">
                我们采取以下措施保护您的数据安全：
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>所有敏感数据仅存储在您的本地设备</li>
                <li>支持 HTTPS 连接确保传输安全</li>
                <li>支持证书指纹验证防止中间人攻击</li>
                <li>不会将您的配置信息发送到第三方服务器</li>
              </ul>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">4. 第三方服务</h2>
              <p className="text-muted-foreground leading-relaxed">
                RayBoxUI 可能会连接到您指定的第三方节点管理面板（如 x-ui、3x-ui、s-ui）。
                这些连接完全由您控制，我们不会监控或记录这些连接的内容。
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">5. 数据删除</h2>
              <p className="text-muted-foreground leading-relaxed">
                您可以随时删除应用中存储的所有配置数据。卸载应用将自动删除所有本地存储的数据。
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">6. 儿童隐私</h2>
              <p className="text-muted-foreground leading-relaxed">
                我们的服务不面向13岁以下的儿童。我们不会故意收集13岁以下儿童的个人信息。
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">7. 政策更新</h2>
              <p className="text-muted-foreground leading-relaxed">
                我们可能会不时更新此隐私政策。任何更改都将在此页面上发布，并更新&ldquo;最后更新&rdquo;日期。
              </p>
            </section>

            <section>
              <h2 className="text-xl font-semibold mb-3 text-foreground">8. 联系我们</h2>
              <p className="text-muted-foreground leading-relaxed">
                如果您对此隐私政策有任何疑问，请通过应用内的反馈功能联系我们。
              </p>
            </section>

            <div className="mt-8 p-4 bg-muted rounded-lg border border-border">
              <p className="text-sm text-foreground">
                <strong>重要提示：</strong>
                RayBoxUI 是一个注重隐私的应用。我们承诺不会收集、存储或分享您的个人数据。
                所有配置信息都安全地存储在您的设备上。
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
