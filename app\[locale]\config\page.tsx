"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UIConfig, SUIConfig, XUIConfig, ThreeXUIConfig } from "@/lib/types";
import { parseUrl } from "@/lib/url-utils";
import { ConfigPreview } from "@/components/config-preview";
import { Navigation } from "@/components/navigation";
import { PageHeader } from "@/components/page-header";

export default function ConfigPage() {
  const [configType, setConfigType] = useState<"s-ui" | "x-ui" | "3x-ui">("s-ui");
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    username: "",
    password: "",
    api: "",
    certFingerprints: "",
  });
  const [generatedConfig, setGeneratedConfig] = useState<UIConfig | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "请输入配置名称";
    }

    if (!formData.url.trim()) {
      newErrors.url = "请输入完整URL";
    } else {
      try {
        parseUrl(formData.url);
      } catch (error) {
        newErrors.url = error instanceof Error ? error.message : "URL格式错误";
      }
    }

    if (configType !== "s-ui") {
      if (!formData.username.trim()) {
        newErrors.username = "请输入用户名";
      }
      if (!formData.password.trim()) {
        newErrors.password = "请输入密码";
      }
    }

    if (configType === "s-ui" && !formData.api.trim()) {
      newErrors.api = "请输入API密钥";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const generateConfig = () => {
    if (!validateForm()) return;

    try {
      const parsedUrl = parseUrl(formData.url);
      const certFingerprints = formData.certFingerprints
        .split('\n')
        .map(fp => fp.trim())
        .filter(fp => fp.length > 0);

      const baseConfig = {
        name: formData.name,
        url: formData.url.replace(/^https?:\/\//, ""),
        protocol: parsedUrl.protocol,
        certFingerprints: certFingerprints.length > 0 ? certFingerprints : undefined,
      };

      let config: UIConfig;

      switch (configType) {
        case "s-ui":
          config = {
            ...baseConfig,
            type: "s-ui",
            api: formData.api,
          } as SUIConfig;
          break;
        case "x-ui":
          config = {
            ...baseConfig,
            type: "x-ui",
            username: formData.username,
            password: formData.password,
          } as XUIConfig;
          break;
        case "3x-ui":
          config = {
            ...baseConfig,
            type: "3x-ui",
            username: formData.username,
            password: formData.password,
          } as ThreeXUIConfig;
          break;
      }

      setGeneratedConfig(config);
    } catch (error) {
      setErrors({ url: error instanceof Error ? error.message : "配置生成失败" });
    }
  };

  const downloadConfig = () => {
    if (!generatedConfig) return;

    const dataStr = JSON.stringify(generatedConfig, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${generatedConfig.name}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <Navigation />
      <PageHeader
        title="节点配置"
        description="创建和管理您的节点配置，支持 s-ui、x-ui、3x-ui 多种面板类型"
      />

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Configuration Form */}
          <Card>
            <CardHeader>
              <CardTitle>创建配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Panel Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="type">面板类型</Label>
                <Select value={configType} onValueChange={(value: "s-ui" | "x-ui" | "3x-ui") => setConfigType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="s-ui">s-ui</SelectItem>
                    <SelectItem value="x-ui">x-ui</SelectItem>
                    <SelectItem value="3x-ui">3x-ui</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Configuration Name */}
              <div className="space-y-2">
                <Label htmlFor="name">配置名称</Label>
                <Input
                  id="name"
                  placeholder="输入配置名称"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              {/* URL */}
              <div className="space-y-2">
                <Label htmlFor="url">完整URL</Label>
                <Input
                  id="url"
                  placeholder="https://example.com:8080"
                  value={formData.url}
                  onChange={(e) => handleInputChange("url", e.target.value)}
                />
                {errors.url && <p className="text-sm text-red-500">{errors.url}</p>}
                <p className="text-xs text-gray-500">包含协议、域名/IP和端口</p>
              </div>

              {/* Username and Password for x-ui and 3x-ui */}
              {configType !== "s-ui" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="username">用户名</Label>
                    <Input
                      id="username"
                      placeholder="输入用户名"
                      value={formData.username}
                      onChange={(e) => handleInputChange("username", e.target.value)}
                    />
                    {errors.username && <p className="text-sm text-red-500">{errors.username}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">密码</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="输入密码"
                      value={formData.password}
                      onChange={(e) => handleInputChange("password", e.target.value)}
                    />
                    {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                  </div>
                </>
              )}

              {/* API Key for s-ui */}
              {configType === "s-ui" && (
                <div className="space-y-2">
                  <Label htmlFor="api">API密钥</Label>
                  <Input
                    id="api"
                    placeholder="输入API密钥"
                    value={formData.api}
                    onChange={(e) => handleInputChange("api", e.target.value)}
                  />
                  {errors.api && <p className="text-sm text-red-500">{errors.api}</p>}
                </div>
              )}

              {/* Certificate Fingerprints */}
              <div className="space-y-2">
                <Label htmlFor="certFingerprints">证书指纹 (可选)</Label>
                <Textarea
                  id="certFingerprints"
                  placeholder="每行一个SHA256指纹"
                  rows={3}
                  value={formData.certFingerprints}
                  onChange={(e) => handleInputChange("certFingerprints", e.target.value)}
                />
                <p className="text-xs text-gray-500">用于验证服务器证书，每行一个</p>
              </div>

              <Button onClick={generateConfig} className="w-full">
                生成配置
              </Button>
            </CardContent>
          </Card>

          {/* Generated Config and QR Code */}
          {generatedConfig && (
            <ConfigPreview
              config={generatedConfig}
              onDownload={downloadConfig}
            />
          )}
        </div>
      </main>
    </div>
  );
}
